import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { requireAdmin } from '$lib/server/admin.js';
import { getAllUsers, getUsersCount, createUserByAdmin, getUserById, updateUserPassword } from '$lib/server/db/operations.js';
import { generateRandomPassword, hashPassword } from '$lib/server/auth.js';
import { sendEmail } from '$lib/server/email.js';

export const GET: RequestHandler = async (event) => {
  try {
    await requireAdmin(event);

    const users = await getAllUsers();
    const totalCount = await getUsersCount();

    // Remove sensitive information
    const safeUsers = users.map(user => ({
      id: user.id,
      email: user.email,
      name: user.name,
      isVerified: user.isVerified,
      isAdmin: user.isAdmin,
      createdAt: user.createdAt,
      timezone: user.timezone
    }));

    return json({
      users: safeUsers,
      totalCount
    });
  } catch (error) {
    console.error('Admin get users error:', error);
    return json({ error: 'Unauthorized' }, { status: 401 });
  }
};

export const POST: RequestHandler = async (event) => {
  try {
    await requireAdmin(event);

    const { email } = await event.request.json();

    if (!email) {
      return json({ error: 'Email is required' }, { status: 400 });
    }

    // Generate random password
    const password = generateRandomPassword();
    const hashedPassword = await hashPassword(password);

    // Create user
    const user = await createUserByAdmin({
      email,
      name: null,
      password: hashedPassword
    });

    // Send welcome email with credentials
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #4299e1, #63b3ed); padding: 2rem; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 1.75rem;">Routine Mail</h1>
        </div>
        
        <div style="padding: 2rem; background: white;">
          <h2 style="color: #2d3748; margin-bottom: 1rem;">Welcome to Routine Mail!</h2>
          
          <p style="color: #4a5568; line-height: 1.6; margin-bottom: 1rem;">
            An admin has created an account for you. Here are your login credentials:
          </p>

          <div style="background: #f7fafc; border: 2px solid #e2e8f0; border-radius: 8px; padding: 1.5rem; margin: 1.5rem 0;">
            <p style="margin: 0.5rem 0; color: #2d3748;"><strong>Email:</strong> ${email}</p>
            <p style="margin: 0.5rem 0; color: #2d3748;"><strong>Password:</strong> <code style="background: #e2e8f0; padding: 0.25rem 0.5rem; border-radius: 4px;">${password}</code></p>
          </div>
          
          <p style="color: #4a5568; line-height: 1.6; margin-bottom: 1rem;">
            Please log in and change your password as soon as possible.
          </p>
          
          <div style="text-align: center; margin: 2rem 0;">
            <a href="${process.env.APP_URL || 'http://localhost:5173'}/login" 
               style="background: #4299e1; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 6px; display: inline-block;">
              Login to Routine Mail
            </a>
          </div>
          
          <div style="border-top: 1px solid #e2e8f0; padding-top: 1.5rem; margin-top: 2rem;">
            <p style="color: #718096; font-size: 0.875rem; margin: 0;">
              This is an automated message from Routine Mail. Please do not reply to this email.
            </p>
          </div>
        </div>
      </div>
    `;

    try {
      await sendEmail(
        email,
        'Welcome to Routine Mail - Your Account Details',
        emailHtml
      );
    } catch (emailError) {
      console.error('Failed to send welcome email:', emailError);
      // Don't fail the user creation if email fails
    }

    return json({
      message: 'User created successfully',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        isVerified: user.isVerified,
        isAdmin: user.isAdmin,
        createdAt: user.createdAt
      },
      credentials: {
        password
      }
    });
  } catch (error) {
    console.error('Admin create user error:', error);
    if (error instanceof Error && error.message === 'Admin access required') {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }
    return json({ error: 'Failed to create user' }, { status: 500 });
  }
};

export const PUT: RequestHandler = async (event) => {
  try {
    await requireAdmin(event);

    const { userId, action } = await event.request.json();

    if (!userId) {
      return json({ error: 'User ID is required' }, { status: 400 });
    }

    if (action === 'reset_password') {
      // Get user
      const user = await getUserById(userId);
      if (!user) {
        return json({ error: 'User not found' }, { status: 404 });
      }

      // Generate new password
      const newPassword = generateRandomPassword();
      const hashedPassword = await hashPassword(newPassword);

      // Update password
      await updateUserPassword(userId, hashedPassword);

      // Send password reset email
      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #4299e1, #63b3ed); padding: 2rem; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 1.75rem;">Routine Mail</h1>
          </div>

          <div style="padding: 2rem; background: white;">
            <h2 style="color: #2d3748; margin-bottom: 1rem;">Password Reset</h2>

            <p style="color: #4a5568; line-height: 1.6; margin-bottom: 1rem;">
              Your password has been reset by an administrator. Here is your new password:
            </p>

            <div style="background: #f7fafc; border: 2px solid #e2e8f0; border-radius: 8px; padding: 1.5rem; margin: 1.5rem 0;">
              <p style="margin: 0.5rem 0; color: #2d3748;"><strong>Email:</strong> ${user.email}</p>
              <p style="margin: 0.5rem 0; color: #2d3748;"><strong>New Password:</strong> <code style="background: #e2e8f0; padding: 0.25rem 0.5rem; border-radius: 4px;">${newPassword}</code></p>
            </div>

            <p style="color: #e53e3e; line-height: 1.6; margin-bottom: 1rem;">
              <strong>Important:</strong> Please log in and change your password immediately for security.
            </p>

            <div style="text-align: center; margin: 2rem 0;">
              <a href="${process.env.APP_URL || 'http://localhost:5173'}/login"
                 style="background: #4299e1; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 6px; display: inline-block;">
                Login to Routine Mail
              </a>
            </div>

            <div style="border-top: 1px solid #e2e8f0; padding-top: 1.5rem; margin-top: 2rem;">
              <p style="color: #718096; font-size: 0.875rem; margin: 0;">
                This is an automated message from Routine Mail. Please do not reply to this email.
              </p>
            </div>
          </div>
        </div>
      `;

      try {
        await sendEmail(
          user.email,
          'Routine Mail - Password Reset',
          emailHtml
        );
      } catch (emailError) {
        console.error('Failed to send password reset email:', emailError);
        // Don't fail the password reset if email fails
      }

      return json({
        message: 'Password reset successfully',
        newPassword
      });
    }

    return json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Admin user action error:', error);
    if (error instanceof Error && error.message === 'Admin access required') {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }
    return json({ error: 'Failed to perform action' }, { status: 500 });
  }
};
