import { maintainRecurringInstances, cleanupOldRecurringInstances } from './services/cronJobs.js';

/**
 * Simple in-memory scheduler for recurring tasks maintenance
 * In production, you might want to use a more robust solution like node-cron
 */
class TaskScheduler {
  private intervals: NodeJS.Timeout[] = [];
  private isRunning = false;

  /**
   * Start the scheduler
   */
  start(): void {
    if (this.isRunning) {
      console.log('Scheduler is already running');
      return;
    }

    console.log('Starting task scheduler...');
    this.isRunning = true;

    // Run maintenance every 6 hours (6 * 60 * 60 * 1000 ms)
    const maintenanceInterval = setInterval(async () => {
      try {
        console.log('Running scheduled recurring instances maintenance...');
        await maintainRecurringInstances();
      } catch (error) {
        console.error('Error in scheduled maintenance:', error);
      }
    }, 6 * 60 * 60 * 1000);

    // Run cleanup once daily (24 * 60 * 60 * 1000 ms)
    const cleanupInterval = setInterval(async () => {
      try {
        console.log('Running scheduled cleanup...');
        await cleanupOldRecurringInstances();
      } catch (error) {
        console.error('Error in scheduled cleanup:', error);
      }
    }, 24 * 60 * 60 * 1000);

    this.intervals.push(maintenanceInterval, cleanupInterval);

    // Run initial maintenance on startup
    setTimeout(async () => {
      try {
        console.log('Running initial recurring instances maintenance...');
        await maintainRecurringInstances();
      } catch (error) {
        console.error('Error in initial maintenance:', error);
      }
    }, 5000); // Wait 5 seconds after startup

    console.log('Task scheduler started successfully');
  }

  /**
   * Stop the scheduler
   */
  stop(): void {
    if (!this.isRunning) {
      console.log('Scheduler is not running');
      return;
    }

    console.log('Stopping task scheduler...');
    
    this.intervals.forEach(interval => {
      clearInterval(interval);
    });
    
    this.intervals = [];
    this.isRunning = false;
    
    console.log('Task scheduler stopped');
  }

  /**
   * Get scheduler status
   */
  getStatus(): { isRunning: boolean; intervalCount: number } {
    return {
      isRunning: this.isRunning,
      intervalCount: this.intervals.length
    };
  }

  /**
   * Manually trigger maintenance (for testing or manual runs)
   */
  async runMaintenance(): Promise<void> {
    console.log('Manually triggering recurring instances maintenance...');
    try {
      await maintainRecurringInstances();
      console.log('Manual maintenance completed successfully');
    } catch (error) {
      console.error('Error in manual maintenance:', error);
      throw error;
    }
  }

  /**
   * Manually trigger cleanup (for testing or manual runs)
   */
  async runCleanup(): Promise<void> {
    console.log('Manually triggering cleanup...');
    try {
      await cleanupOldRecurringInstances();
      console.log('Manual cleanup completed successfully');
    } catch (error) {
      console.error('Error in manual cleanup:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const taskScheduler = new TaskScheduler();

// Auto-start in production
if (process.env.NODE_ENV === 'production') {
  taskScheduler.start();
}
