import { db } from '../db/index.js';
import { tasks } from '../db/schema.js';
import type { Task } from '../db/schema.js';
import { eq, and, gte, lt, isNull } from 'drizzle-orm';
import { generateRecurringInstances } from './recurringTasks.js';

/**
 * Daily cron job to maintain recurring task instances
 * This ensures we always have instances generated for the next 3 months
 */
export async function maintainRecurringInstances(): Promise<void> {
  console.log('Starting daily recurring instances maintenance...');

  try {
    // Get all recurring template tasks
    const templates = await db
      .select()
      .from(tasks)
      .where(eq(tasks.isRecurringTemplate, true));

    console.log(`Found ${templates.length} recurring templates to process`);

    for (const template of templates) {
      await maintainTemplateInstances(template);
    }

    console.log('Completed daily recurring instances maintenance');
  } catch (error) {
    console.error('Error in maintainRecurringInstances:', error);
    throw error;
  }
}

/**
 * Maintain instances for a specific template
 */
async function maintainTemplateInstances(template: Task): Promise<void> {
  if (!template.recurringGroupId || !template.recurrenceRule || !template.dueDate) {
    console.log(`Skipping template ${template.id}: missing required fields`);
    return;
  }

  // Check the latest instance date for this template
  const existingInstances = await db
    .select()
    .from(tasks)
    .where(
      and(
        eq(tasks.recurringGroupId, template.recurringGroupId),
        eq(tasks.isRecurringTemplate, false)
      )
    )
    .orderBy(tasks.recurringInstanceDate);

  // Determine the date range we need to cover
  const now = new Date();
  const targetEndDate = new Date();
  targetEndDate.setMonth(targetEndDate.getMonth() + 3); // Always maintain 3 months ahead

  let startGenerationDate: Date;

  if (existingInstances.length === 0) {
    // No instances exist, start from template due date
    startGenerationDate = new Date(template.dueDate);
  } else {
    // Find the latest instance date and start from the next occurrence
    const latestInstance = existingInstances[existingInstances.length - 1];
    if (latestInstance.recurringInstanceDate) {
      startGenerationDate = new Date(latestInstance.recurringInstanceDate);
      // Move to next occurrence
      startGenerationDate = getNextOccurrence(startGenerationDate, template.recurrenceRule as any);
    } else {
      startGenerationDate = new Date(template.dueDate);
    }
  }

  // Only generate if we need instances beyond what we have
  if (startGenerationDate <= targetEndDate) {
    // Create a temporary template with the start date for generation
    const tempTemplate = {
      ...template,
      dueDate: startGenerationDate
    };

    // Get user timezone for recurring task generation
    const { getUserById } = await import('../db/operations.js');
    const user = await getUserById(template.userId);
    const userTimezone = user?.timezone || 'Asia/Kuala_Lumpur';

    const newInstances = await generateRecurringInstances(tempTemplate, targetEndDate, userTimezone);

    if (newInstances.length > 0) {
      await db.insert(tasks).values(newInstances);
      console.log(`Generated ${newInstances.length} new instances for template ${template.id}`);
    }
  }
}

/**
 * Calculate next occurrence date based on recurrence rule
 * (Simplified version - you may want to import from recurringTasks.ts)
 */
function getNextOccurrence(currentDate: Date, rule: any): Date {
  const nextDate = new Date(currentDate);

  switch (rule.type) {
    case 'daily':
      nextDate.setDate(nextDate.getDate() + (rule.interval || 1));
      break;

    case 'weekly':
      if (rule.weekdays && rule.weekdays.length > 0) {
        // Find next weekday
        const currentDay = nextDate.getDay();
        const sortedWeekdays = [...rule.weekdays].sort((a, b) => a - b);

        let nextWeekday = sortedWeekdays.find(day => day > currentDay);
        if (!nextWeekday) {
          // Move to next week, first weekday
          nextWeekday = sortedWeekdays[0];
          nextDate.setDate(nextDate.getDate() + (7 - currentDay + nextWeekday));
        } else {
          nextDate.setDate(nextDate.getDate() + (nextWeekday - currentDay));
        }
      } else {
        nextDate.setDate(nextDate.getDate() + 7 * (rule.interval || 1));
      }
      break;

    case 'monthly':
      nextDate.setMonth(nextDate.getMonth() + (rule.interval || 1));
      break;

    case 'yearly':
      nextDate.setFullYear(nextDate.getFullYear() + (rule.interval || 1));
      break;

    default:
      nextDate.setDate(nextDate.getDate() + 1);
  }

  return nextDate;
}

/**
 * Clean up old completed recurring instances
 * Keep only the last 30 days of completed instances to prevent database bloat
 * This removes old completed recurring task instances to keep the database clean
 */
export async function cleanupOldRecurringInstances(): Promise<void> {
  console.log('Starting cleanup of old completed recurring instances...');

  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - 30); // Keep last 30 days of completed tasks

  try {
    const result = await db.delete(tasks).where(
      and(
        eq(tasks.isRecurringTemplate, false),
        eq(tasks.completed, true),
        lt(tasks.completedAt, cutoffDate),
        // Only delete if it's part of a recurring group
        isNull(tasks.recurringGroupId) === false
      )
    );

    console.log(`Cleaned up old recurring instances`);
  } catch (error) {
    console.error('Error in cleanupOldRecurringInstances:', error);
    throw error;
  }
}
