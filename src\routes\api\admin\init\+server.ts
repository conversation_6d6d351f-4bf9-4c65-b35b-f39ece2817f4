import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { getAdminUser, getUserByEmail, createUserByAdmin, updateUserAdminStatus } from '$lib/server/db/operations.js';
import { generateRandomPassword, hashPassword, isAdminEmail } from '$lib/server/auth.js';
import { sendEmail } from '$lib/server/email.js';

export const POST: RequestHandler = async () => {
  try {
    const adminEmail = process.env.ADMIN_EMAIL;
    
    if (!adminEmail) {
      return json({ error: 'ADMIN_EMAIL not configured' }, { status: 500 });
    }

    // Check if admin already exists
    const existingAdmin = await getAdminUser();
    if (existingAdmin) {
      return json({ message: 'Admin already exists', adminEmail: existingAdmin.email });
    }

    // Check if user with admin email exists
    let adminUser = await getUserByEmail(adminEmail);
    
    if (adminUser) {
      // User exists, just make them admin
      await updateUserAdminStatus(adminUser.id, true);
      
      return json({ 
        message: 'Existing user promoted to admin', 
        adminEmail: adminUser.email 
      });
    } else {
      // Create new admin user
      const password = generateRandomPassword();
      const hashedPassword = await hashPassword(password);

      adminUser = await createUserByAdmin({
        email: adminEmail,
        name: 'Administrator',
        password: hashedPassword,
        isAdmin: true
      });

      // Send admin credentials email
      const emailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: linear-gradient(135deg, #4299e1, #63b3ed); padding: 2rem; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 1.75rem;">Routine Mail</h1>
          </div>
          
          <div style="padding: 2rem; background: white;">
            <h2 style="color: #2d3748; margin-bottom: 1rem;">Admin Account Created</h2>
            
            <p style="color: #4a5568; line-height: 1.6; margin-bottom: 1rem;">
              Your admin account has been automatically created for Routine Mail. Here are your login credentials:
            </p>
            
            <div style="background: #f7fafc; border: 2px solid #e2e8f0; border-radius: 8px; padding: 1.5rem; margin: 1.5rem 0;">
              <p style="margin: 0.5rem 0; color: #2d3748;"><strong>Email:</strong> ${adminEmail}</p>
              <p style="margin: 0.5rem 0; color: #2d3748;"><strong>Password:</strong> <code style="background: #e2e8f0; padding: 0.25rem 0.5rem; border-radius: 4px;">${password}</code></p>
            </div>
            
            <p style="color: #e53e3e; line-height: 1.6; margin-bottom: 1rem;">
              <strong>Important:</strong> Please log in and change your password immediately for security.
            </p>
            
            <div style="text-align: center; margin: 2rem 0;">
              <a href="${process.env.APP_URL || 'http://localhost:5173'}/login" 
                 style="background: #4299e1; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 6px; display: inline-block;">
                Login as Admin
              </a>
            </div>
            
            <div style="border-top: 1px solid #e2e8f0; padding-top: 1.5rem; margin-top: 2rem;">
              <p style="color: #718096; font-size: 0.875rem; margin: 0;">
                This is an automated message from Routine Mail. Please do not reply to this email.
              </p>
            </div>
          </div>
        </div>
      `;

      try {
        await sendEmail(
          adminEmail,
          'Routine Mail - Admin Account Created',
          emailHtml
        );
        
        console.log(`Admin account created and credentials sent to ${adminEmail}`);
        
        return json({ 
          message: 'Admin account created and credentials sent via email', 
          adminEmail: adminUser.email 
        });
      } catch (emailError) {
        console.error('Failed to send admin credentials email:', emailError);
        
        return json({ 
          message: 'Admin account created but failed to send email', 
          adminEmail: adminUser.email,
          password // Include password in response if email fails
        });
      }
    }
  } catch (error) {
    console.error('Admin initialization error:', error);
    return json({ error: 'Failed to initialize admin' }, { status: 500 });
  }
};
