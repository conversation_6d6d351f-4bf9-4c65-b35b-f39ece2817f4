import { db } from '../db/index.js';
import { users, tasks, taskReminders, emailLogs } from '../db/schema.js';
import type { User, Task, TaskReminder } from '../db/schema.js';
import { eq, and, gte, lt, lte } from 'drizzle-orm';
import { getTasksNeedingReminders, markReminderSent } from './recurringTasks.js';

interface EmailReminderData {
  user: User;
  upcomingTasks: Task[];
  overdueTasks: Task[];
}

/**
 * Get all users who need email reminders today
 */
export async function getUsersNeedingReminders(): Promise<User[]> {
  // Get all verified users who have email reminders enabled
  return await db.select()
    .from(users)
    .where(
      and(
        eq(users.isVerified, true),
        eq(users.emailRemindersEnabled, true)
      )
    );
}

/**
 * Get tasks for email reminder for a specific user
 */
export async function getTasksForEmailReminder(userId: string, userTimezone: string = 'Asia/Kuala_Lumpur'): Promise<EmailReminderData | null> {
  const user = await db.select().from(users).where(eq(users.id, userId)).then(rows => rows[0]);
  if (!user) return null;

  const now = new Date();
  const today = new Date(now);
  today.setHours(0, 0, 0, 0);

  const threeDaysFromNow = new Date(today);
  threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

  // Get upcoming tasks (due within reminder period)
  const upcomingTasks = await db.select()
    .from(tasks)
    .where(
      and(
        eq(tasks.userId, userId),
        eq(tasks.completed, false),
        gte(tasks.dueDate, today),
        lte(tasks.dueDate, threeDaysFromNow),
        eq(tasks.isRecurringTemplate, false) // Exclude templates
      )
    );

  // Get overdue tasks (due before today, within 3 days)
  const threeDaysAgo = new Date(today);
  threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

  const overdueTasks = await db.select()
    .from(tasks)
    .where(
      and(
        eq(tasks.userId, userId),
        eq(tasks.completed, false),
        lt(tasks.dueDate, today),
        gte(tasks.dueDate, threeDaysAgo),
        eq(tasks.isRecurringTemplate, false) // Exclude templates
      )
    );

  return {
    user,
    upcomingTasks,
    overdueTasks
  };
}

/**
 * Generate HTML email content for task reminders
 */
export function generateReminderEmailHTML(data: EmailReminderData): string {
  const { user, upcomingTasks, overdueTasks } = data;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatPriority = (priority: number) => {
    switch (priority) {
      case 0: return '🔵 Low';
      case 1: return '🟡 Normal';
      case 2: return '🔴 High';
      default: return '🟡 Normal';
    }
  };

  let html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Task Reminders - Routine Mail</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 24px; border-radius: 8px 8px 0 0; text-align: center; }
        .content { padding: 24px; }
        .section { margin-bottom: 24px; }
        .section h2 { color: #1f2937; margin-bottom: 12px; font-size: 18px; }
        .task-item { background: #f8fafc; border: 1px solid #e5e7eb; border-radius: 6px; padding: 12px; margin-bottom: 8px; }
        .task-title { font-weight: 600; color: #1f2937; margin-bottom: 4px; }
        .task-meta { font-size: 14px; color: #6b7280; }
        .overdue { border-left: 4px solid #ef4444; }
        .upcoming { border-left: 4px solid #3b82f6; }
        .footer { background: #f8fafc; padding: 16px 24px; border-radius: 0 0 8px 8px; text-align: center; font-size: 14px; color: #6b7280; }
        .no-tasks { text-align: center; color: #6b7280; font-style: italic; padding: 20px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>📋 Task Reminders</h1>
          <p>Good morning, ${user.name || user.email}!</p>
        </div>
        <div class="content">
  `;

  // Overdue tasks section
  if (overdueTasks.length > 0) {
    html += `
      <div class="section">
        <h2>🚨 Overdue Tasks</h2>
    `;

    overdueTasks.forEach(task => {
      html += `
        <div class="task-item overdue">
          <div class="task-title">${task.title}</div>
          <div class="task-meta">
            ${formatPriority(task.priority || 1)} • Due: ${task.dueDate ? formatDate(new Date(task.dueDate)) : 'No date'}
          </div>
        </div>
      `;
    });

    html += `</div>`;
  }

  // Upcoming tasks section
  if (upcomingTasks.length > 0) {
    html += `
      <div class="section">
        <h2>📅 Upcoming Tasks</h2>
    `;

    upcomingTasks.forEach(task => {
      html += `
        <div class="task-item upcoming">
          <div class="task-title">${task.title}</div>
          <div class="task-meta">
            ${formatPriority(task.priority || 1)} • Due: ${task.dueDate ? formatDate(new Date(task.dueDate)) : 'No date'}
          </div>
        </div>
      `;
    });

    html += `</div>`;
  }

  // No tasks message
  if (upcomingTasks.length === 0 && overdueTasks.length === 0) {
    html += `
      <div class="no-tasks">
        🎉 No urgent tasks today! You're all caught up.
      </div>
    `;
  }

  html += `
        </div>
        <div class="footer">
          <p>This is your daily task reminder from Routine Mail.</p>
          <p>Manage your tasks at <a href="${process.env.PUBLIC_APP_URL || 'https://routine-mail.com'}" style="color: #3b82f6;">Routine Mail</a></p>
        </div>
      </div>
    </body>
    </html>
  `;

  return html;
}

/**
 * Send daily reminder emails to all users
 */
export async function sendDailyReminders(): Promise<void> {
  console.log('Starting daily reminder email process...');

  const users = await getUsersNeedingReminders();
  console.log(`Found ${users.length} users to process`);

  for (const user of users) {
    try {
      const reminderData = await getTasksForEmailReminder(user.id, user.timezone || 'Asia/Kuala_Lumpur');

      if (!reminderData) continue;

      const { upcomingTasks, overdueTasks } = reminderData;
      const totalTasks = upcomingTasks.length + overdueTasks.length;

      // Only send email if there are tasks to remind about
      if (totalTasks > 0) {
        const emailHTML = generateReminderEmailHTML(reminderData);

        // TODO: Implement actual email sending
        // For now, just log the email content
        console.log(`Would send email to ${user.email} with ${totalTasks} tasks`);

        // Log the email attempt
        await db.insert(emailLogs).values({
          userId: user.id,
          emailType: 'daily_reminder',
          taskCount: totalTasks,
          success: true
        });
      }
    } catch (error) {
      console.error(`Failed to send reminder to ${user.email}:`, error);

      // Log the failed attempt
      await db.insert(emailLogs).values({
        userId: user.id,
        emailType: 'daily_reminder',
        taskCount: 0,
        success: false
      });
    }
  }

  console.log('Daily reminder email process completed');
}

/**
 * Process specific reminder notifications
 */
export async function processReminderNotifications(): Promise<void> {
  const reminders = await getTasksNeedingReminders();

  for (const reminder of reminders) {
    try {
      // Get the task and user details
      const [task] = await db.select().from(tasks).where(eq(tasks.id, reminder.taskId));
      const [user] = await db.select().from(users).where(eq(users.id, reminder.userId));

      if (task && user) {
        // TODO: Send specific reminder email
        console.log(`Sending reminder for task "${task.title}" to ${user.email}`);

        // Mark reminder as sent
        await markReminderSent(reminder.id);
      }
    } catch (error) {
      console.error(`Failed to process reminder ${reminder.id}:`, error);
    }
  }
}
